'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChatInterface } from './chat-interface';
import { QuickActions } from './quick-actions';
import { InsightsFeed } from './insights-feed';
import ErrorBoundary, { CopilotErrorFallback } from './error-boundary';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export function CopilotPage() {
  const [activeQuery, setActiveQuery] = useState<string>('');
  const [showSidebars, setShowSidebars] = useState(false);

  return (
    <div className="h-full bg-background">
      {/* Minimal Header */}
      <div className="flex items-center justify-between px-8 py-6 border-b border-border/40">
        <div>
          <h1 className="text-xl font-medium text-foreground">AI Copilot</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Ask questions about your portfolio
          </p>
        </div>

        <Link href="/">
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Portfolio
          </Button>
        </Link>
      </div>

      {/* Clean Main Layout */}
      <div className="flex h-[calc(100vh-89px)]">
        {/* Collapsible Quick Actions */}
        {showSidebars && (
          <div className="w-80 border-r border-border/40 bg-muted/20">
            <QuickActions onQuerySelect={setActiveQuery} />
          </div>
        )}

        {/* Central Chat Interface */}
        <div className="flex-1 flex flex-col">
          <ChatInterface
            activeQuery={activeQuery}
            onToggleSidebars={() => setShowSidebars(!showSidebars)}
            showSidebars={showSidebars}
          />
        </div>

        {/* Collapsible Insights */}
        {showSidebars && (
          <div className="w-80 border-l border-border/40 bg-muted/20">
            <InsightsFeed />
          </div>
        )}
      </div>
    </div>
  );
}
